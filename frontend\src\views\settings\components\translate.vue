<template>
  <div class="w-500px h-full">
    <div class="font-size-18px font-bold">翻译设置</div>
    <div class="txt">以下设置将作为新创建会话的默认翻译设置</div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">接收自动翻译设置</div>
      <div>
        <el-switch v-model="temlglobalSwitch.receiveTranslationEnabled" :active-value="1"
                   :inactive-value="0"></el-switch>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译路线</div>
      <div class="mt">
        <el-select v-model="temlglobalSwitch.receiveTranslationConfig.service" placeholder="请选择翻译路线" @change="onReceiveRouteChange">
          <el-option v-for="item in trRoutes" :key="item.software" :label="item.chineseName"
                     :value="String(item.software)">
            <div><span class="text-sm mr-1">{{ item.chineseName }} </span> <i v-if="item.chineseName.includes('Qwen')"
                                                                              class="bg-red-500 text-white rounded-full px-2 py-1 text-xs">推荐</i>
            </div>
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译语言</div>
      <div class="mt">
        <el-select v-model="temlglobalSwitch.receiveTranslationConfig.language" placeholder="请选择翻译语言">
          <el-option
              v-for="language in receiveLanguages"
              :key="language.languageCode"
              :label="language.chineseName"
              :value="language.languageCode">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">群组自动翻译</div>
      <div>
        <el-switch v-model="temlglobalSwitch.groupAutoTranslationEnabled" :active-value="1"
                   :inactive-value="0"></el-switch>
      </div>
    </div>
    <div class="w-full mt9 flex justify-between items-center">
      <div class="font-size-14px font-bold">发送自动翻译设置</div>
      <div>
        <el-switch v-model="temlglobalSwitch.sendTranslationEnabled" :active-value="1" :inactive-value="0"></el-switch>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译路线</div>
      <div class="mt">
        <el-select v-model="temlglobalSwitch.sendTranslationConfig.service" placeholder="请选择翻译路线" @change="onSendRouteChange">
          <el-option v-for="item in trRoutes" :key="item.software" :label="item.chineseName"
                     :value="String(item.software)">
            <div><span class="text-sm mr-1">{{ item.chineseName }} </span> <i v-if="item.chineseName.includes('Qwen')"
                                                                              class="bg-red-500 text-white rounded-full px-2 py-1 text-xs">推荐</i>
            </div>
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="w-full mt4">
      <div class="txt">翻译语言</div>
      <div class="mt">
        <el-select v-model="temlglobalSwitch.sendTranslationConfig.language" placeholder="请选择翻译语言">
          <el-option
              v-for="language in sendLanguages"
              :key="language.languageCode"
              :label="language.chineseName"
              :value="language.languageCode">
          </el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {trLanguageList, trRouteList} from '@/api/setting/index.ts';
import type {TranslateLanguage, TranslateRoute} from '@/api/setting/types.ts';
import {useChatStore} from '@/stores/modules/chat';
import { useDebounceFn } from '@vueuse/core';

const userChat = useChatStore();
// 使用 toRef 保持响应式连接，或者直接使用 computed
const temlglobalSwitch = toRef(userChat, 'globalSwitch');
const trRoutes = ref<TranslateRoute[]>([]);
const receiveLanguages = ref<TranslateLanguage[]>([]);
const sendLanguages = ref<TranslateLanguage[]>([]);

const getTranslateRoutes = async () => {
  try {
    const result = await trRouteList() as any;
    console.log(result)
    if (result?.data) {
      trRoutes.value = result.data;
    }
  } catch (error) {
    console.error('获取翻译路线失败:', error);
  }
};

const getReceiveLanguages = async (type: number) => {
  try {
    const result = await trLanguageList({type}) as any;
    console.log('接收翻译语言:', result)
    if (result?.data) {
      receiveLanguages.value = result.data;
    }
  } catch (error) {
    console.error('获取接收翻译语言失败:', error);
  }
};

const getSendLanguages = async (type: number) => {
  try {
    const result = await trLanguageList({type}) as any;
    console.log('发送翻译语言:', result)
    if (result?.data) {
      sendLanguages.value = result.data;
    }
  } catch (error) {
    console.error('获取发送翻译语言失败:', error);
  }
};

// 接收路线变化时重新获取语言列表
const onReceiveRouteChange = async (service: string) => {
  if (service) {
    await getReceiveLanguages(Number(service));
    // 路线变化时清空之前选择的语言
    temlglobalSwitch.value.receiveTranslationConfig.language = '';
  }
};

// 发送路线变化时重新获取语言列表
const onSendRouteChange = async (service: string) => {
  if (service) {
    await getSendLanguages(Number(service));
    // 路线变化时清空之前选择的语言
    temlglobalSwitch.value.sendTranslationConfig.language = '';
  }
};

// 保存设置到后端
const saveSettings = async () => {
  try {
    await userChat.updateGlobalSwitch(temlglobalSwitch.value);
    console.log('设置保存成功');
  } catch (error) {
    console.error('保存设置失败:', error);
  }
};

// 使用防抖来避免频繁保存
const debouncedSave = useDebounceFn(saveSettings, 1000);

onMounted(async () => {
  await getTranslateRoutes();
  await userChat.getGlobalSwitch();

  // 获取全局设置后，分别根据接收和发送的路线请求对应的翻译语言
  const receiveService = temlglobalSwitch.value.receiveTranslationConfig?.service;
  const sendService = temlglobalSwitch.value.sendTranslationConfig?.service;
  
  if (receiveService) {
    await getReceiveLanguages(Number(receiveService));
  }
  
  if (sendService) {
    await getSendLanguages(Number(sendService));
  }

  // 数据初始化完成后再开始监听变化，避免初始化时触发更新接口
  nextTick(() => {
    watch(temlglobalSwitch, () => {
      debouncedSave();
    }, { deep: true });
  });
});

</script>
<style lang="scss" scoped>
.txt {
  @apply text-#8F959E font-size-14px mt-10px;
}
</style>
