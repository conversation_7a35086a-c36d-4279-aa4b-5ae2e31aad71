<template>
  <div class="contact-setting">
    <!-- 操作菜单下拉 -->
    <el-dropdown popper-class="a2c-dropdown">
      <el-button link size="small" class="ghost-button h-5">
        <i class="iconfont icon-arrow_left text-2xl leading-none"></i>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in settingList" :key="item.value" @click="handleSettingClick(item.value)">
            <span class="text-sm">{{ item.label }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const settingList = computed(() => [
  {
    label: '置顶对话',
    value: 'top',
  },
  {
    label: '为对话添加标签',
    value: 'tag',
  }
]);

const handleSettingClick = (value: string) => {
  console.log(value);
};
</script>

<style lang="css" scoped>
.contact-setting {
  position: absolute;
  right: 0;
  transform: translateX(100%) scale(0);
  transform-origin: center;
  transition: transform 0.3s ease-in-out;
  opacity: 0;
  pointer-events: none;
}
</style>