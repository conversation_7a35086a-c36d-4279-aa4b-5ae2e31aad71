<template>
  <el-drawer :model-value="modelValue" @update:model-value="handleUpdateModelValue" :title="title" :show-close="false" size="100%" direction="ltr" :modal="false" modal-class="part-drawer" @close="handleClose">
    <template #header="{ close }">
      <div class="flex-between">
        <el-button link size="small" class="ghost-button h-5" @click="handleClose">
          <i class="iconfont icon-close text-base leading-none"></i>
        </el-button>
        <h3 class="text-base flex-1 text-gray-600 pl-5">{{ title }}</h3>
        <el-button v-if="showConfirmBtn" type="primary" round :loading="confirmLoading" @click="handleConfirm">
          {{ confirmText }}
        </el-button>
      </div>
    </template>
    <div class="drawer-content h-full overflow-y-auto" v-loading="confirmLoading">
      <slot></slot>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean;
  title?: string;
  showConfirmBtn?: boolean;
  confirmText?: string;
  confirmLoading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'close'): void;
  (e: 'confirm'): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showConfirmBtn: true,
  confirmText: '确定',
  confirmLoading: false
});

const emit = defineEmits<Emits>();

const handleUpdateModelValue = (value: boolean) => {
  emit('update:modelValue', value);
};

const handleClose = () => {
  emit('update:modelValue', false);
  emit('close');
};

const handleConfirm = () => {
  emit('confirm');
};
</script>
