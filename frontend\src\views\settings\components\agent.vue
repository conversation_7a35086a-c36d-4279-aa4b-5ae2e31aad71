<template>
  <div class="w-500px h-full pb10">
    <div class="font-size-18px font-bold">代理设置 (全局）</div>
    <div class="txt">全局代理设置，对于未进行单独设置的会话，代理的设置和全局代理设置的一致</div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top">
      <el-form-item class="w-full mt9 flex justify-between items-center custom-switch-item">
        <div class="font-size-14px font-bold">启用代理服务器</div>
        <el-switch v-model="formData.isActive" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item class="w-full mt4" label="协议">
        <div class="w-full">
          <el-select v-model="formData.ipMold" class="w-full" placeholder="请选择协议">
            <el-option label="http" value="http"></el-option>
            <el-option label="socks5" value="socks5"></el-option>
          </el-select>
        </div>
      </el-form-item>

      <el-form-item class="w-full mt4" label="主机" prop="host">
        <div class="w-full">
          <el-input v-model="formData.host" placeholder="请输入主机地址"></el-input>
        </div>
      </el-form-item>

      <el-form-item class="w-full mt4" label="端口" prop="port">
        <div class="w-full">
          <el-input
              v-model.number="formData.port"
              placeholder="请输入端口号"
              type="number"
              @input="(value) => { formData.port = Number(value) || 0 }"
          ></el-input>
        </div>
      </el-form-item>

      <el-form-item class="w-full mt9 flex justify-between items-center custom-switch-item">
        <div class="font-size-14px font-bold">启动代理服务器验证</div>
        <el-switch v-model="formData.enableAuth"></el-switch>
      </el-form-item>

      <el-form-item class="w-full mt4" label="用户名">
        <div class="w-full">
          <el-input v-model="formData.userId" :disabled="!formData.enableAuth" placeholder="请输入用户名"></el-input>
        </div>
      </el-form-item>

      <el-form-item class="w-full mt4" label="密码">
        <div class="w-full">
          <el-input v-model="formData.password" :disabled="!formData.enableAuth" placeholder="请输入密码" show-password
                    type="password"></el-input>
        </div>
      </el-form-item>
    </el-form>
    <div class="w-full mt8 text-right">
      <el-button @click="checkProxy">检查代理服务器</el-button>
      <el-button type="primary" @click="saveSettings">保存</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import type {FormInstance, FormRules} from 'element-plus'
import {ElLoading, ElMessage} from 'element-plus'
import {saveProxyConfig, testProxyConnection} from '@/api/setting'
import {IPConfig} from '@/api/setting/types.ts'
import {useUserInfoStore} from '@/stores/modules/userInfo'

// 使用用户信息store
const userInfoStore = useUserInfoStore()

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据 - 使用store中的代理配置初始化
const formData = ref<IPConfig>({
  isActive: 1,
  ipMold: 'http',
  host: '',
  port: undefined,
  enableAuth: false,
  userId: '',
  password: ''
});

// 监听store中的代理配置变化，更新表单数据
watch(() => userInfoStore.proxyConfig, (newConfig) => {
  if (newConfig) {
    formData.value = {
      isActive: newConfig.isActive,
      ipMold: newConfig.protocol || 'http',
      host: newConfig.host || '',
      port: newConfig.port || undefined,
      enableAuth: !!(newConfig.username && newConfig.password),
      userId: newConfig.username || '',
      password: newConfig.password || ''
    };
    console.log('从store更新表单数据:', formData.value);
  }
}, {immediate: true});

// 验证规则
const rules = reactive<FormRules>({
  host: [
    {required: true, message: '请输入主机地址', trigger: 'blur'},
    {
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/,
      message: '请输入有效的IP地址或域名',
      trigger: 'blur'
    }
  ],
  port: [
    {required: true, message: '请输入端口号', trigger: 'blur'},
    {
      validator: (rule, value, callback) => {
        if (!value && value !== 0) {
          callback(new Error('请输入端口号'))
        } else {
          const port = typeof value === 'string' ? Number(value) : value
          if (isNaN(port) || port < 1 || port > 65535) {
            callback(new Error('端口号必须是1-65535之间的数字'))
          } else {
            callback()
          }
        }
      },
      trigger: 'blur'
    }
  ]
})
// 检查代理服务器
const checkProxy = async () => {
  if (!formRef.value) return

  let loading = null;

  try {
    console.log('开始代理检测...');

    const valid = await formRef.value.validate()
    if (valid) {
      let proxyConfig: IPConfig = {
        ...formData.value,
      }
      delete proxyConfig.isActive

      console.log('代理配置:', proxyConfig);

      // 显示加载状态
      loading = ElLoading.service({
        lock: true,
        text: '正在检测代理服务器连接...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始调用testProxyConnection...');

        console.log(proxyConfig)
        // 进行本地连接测试，无超时限制，让后端自行处理超时
        const localTestResult = await testProxyConnection(proxyConfig);

        console.log('testProxyConnection返回结果:', localTestResult);

        if (localTestResult && localTestResult.status) {
          ElMessage.success({
            message: `代理连接成功！\n响应时间: ${localTestResult.data.responseTime}ms`,
            duration: 5000,
            showClose: true
          });

          console.log('本地代理测试成功:', localTestResult);

        } else {
          const errorMsg = localTestResult?.message || '未知错误';
          ElMessage.error({
            message: `代理连接失败：${errorMsg}`,
            duration: 6000,
            showClose: true
          });
          console.error('代理连接测试失败:', localTestResult);
        }
      } catch (error) {
        console.error('代理检测出错:', error);

        let errorMessage = '代理检测出错：';
        if (error.message && error.message.includes('No handler registered')) {
          errorMessage += '功能未正确初始化，请重启应用后再试';
        } else {
          errorMessage += error.message || '未知错误';
        }

        ElMessage.error({
          message: errorMessage,
          duration: 6000,
          showClose: true
        });
      }
    } else {
      console.log('表单验证失败');
      ElMessage.warning('请检查表单输入是否正确');
    }
  } catch (error) {
    console.error('checkProxy外层异常:', error);
    ElMessage.error({
      message: `检测过程发生错误：${error.message || '未知错误'}`,
      duration: 6000,
      showClose: true
    });
  } finally {
    // 确保loading状态被关闭
    if (loading) {
      try {
        loading.close();
        console.log('Loading状态已关闭');
      } catch (e) {
        console.warn('关闭loading失败:', e);
      }
    }
  }
};

// 保存设置
const saveSettings = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (valid) {
      // 准备要保存的数据，将表单数据转换为API需要的格式
      const saveData = {
        host: formData.value.host,
        port: formData.value.port,
        username: formData.value.userId,
        password: formData.value.password,
        protocol: formData.value.ipMold,
        isActive: formData.value.isActive, // 是否启用代理
      }

      console.log('准备保存的代理配置数据:', saveData)

      try {
        const response: any = await saveProxyConfig(saveData)

        if (response && response.code === 200) {
          ElMessage.success({
            message: '保存成功',
            duration: 2000,
            showClose: true
          })

          // 保存成功后，更新store中的代理配置
          userInfoStore.updateProxyConfig(formData.value)
          console.log('代理配置保存成功并已更新到store')
        } else {
          ElMessage.error({
            message: `保存失败：${response?.msg || '未知错误'}`,
            duration: 3000,
            showClose: true
          })
        }
      } catch (error) {
        console.error('保存代理配置时发生错误:', error)
        ElMessage.error({
          message: `保存失败：${error.message || '未知错误'}`,
          duration: 3000,
          showClose: true
        })
      }
    }
  } catch (error) {
    console.log('表单验证失败', error)
    ElMessage.warning('请检查表单输入是否正确')
  }
}

onMounted(() => {
})
</script>
<style lang="scss" scoped>
.txt {
  @apply text-#8F959E font-size-14px mt-10px;
}

// 自定义开关项样式，保持原有的左右布局
:deep(.custom-switch-item) {
  .el-form-item__content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-left: 0 !important;
  }

  .el-form-item__label {
    margin-bottom: 0 !important;
  }
}

// 确保其他表单项保持原有样式
:deep(.el-form-item) {
  margin-bottom: 0;

  .el-form-item__label {
    padding: 0;
    line-height: normal;
  }

  .el-form-item__content {
    line-height: normal;
  }
}

// 保持选择框和输入框的宽度
:deep(.el-select),
:deep(.el-input) {
  width: 100%;
}
</style>
