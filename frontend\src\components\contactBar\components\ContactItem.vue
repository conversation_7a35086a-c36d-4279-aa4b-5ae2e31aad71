<template>
  <div class="flex-between contact-item">
    <el-avatar v-if="item.headImg" :size="49" :src="`${item.headImg}`" class="flex-shrink-0" />
    <tk-avatar v-else-if="chatStore.isVirtualSeat" :account="item.friendAccount" />
    <span v-else class="icon-avatar"></span>
    <dl class="contact-content" @click.stop="contactItemClick(item)">
      <dt class="flex-between">
        <span class="flex-1 line-clamp-1 text-base" :title="accountEncrypt(item.friendAccount)">
          <i v-if="item.isDuplicateFans" class="text-sm text-orange ml-1 vertical-2px">[重粉]</i>
          {{ accountEncrypt(item.friendAccount) }}
          <i v-if="item.visitorStatus >= 0" class="text-sm ml-1 vertical-2px text-gray" :class="{ 'theme-color': item.visitorStatus === 1 }">{{ item.visitorStatus === 1 ? '在线' : item.visitorStatus === 0 ? '离线' : '' }}</i>
        </span>
        <span class="text-gray-500 text-sm flex-shrink-0" :class="{ 'theme-color': Number(item.unReadNum) > 0 }">
          {{ item.messageVO?.createdAt ? formatChatTime(item.messageVO.createdAt) : '' }}
        </span>
      </dt>
      <dd v-if="item.nickname" class="text-green-800 text-sm flex items-center justify-between">称呼：{{ item.nickname }}</dd>
      <dd class="contact-message">
        <p class="flex items-center flex-1">
          <i v-if="chatStore.currentChat.staffPlatformType === 1 && item.messageVO && item.messageVO?.senderAccount != item.friendAccount" class="read-icon iconfont" :class="item.messageVO?.receiverRead == 1 ? 'icon-yidu text-blue-500' : 'icon-weidu text-gray-400'"></i>
          <span class="pr-2 line-clamp-1">{{ messageTypeTips(item.messageVO) }}</span>
        </p>
        <span v-if="item.unReadNum > 0" class="new-tips flex-shrink-0">{{ formatUnreadCount(item.unReadNum) }}</span>
        <!-- 操作菜单下拉 -->
        <ContactSetting class="contact-setting" />
      </dd>
    </dl>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { formatChatTime } from '@/utils/dateFormat';
import { formatUnreadCount, messageTypeTips } from '@/utils';
import { Contact } from '@/types/contact';
import ContactSetting from '@/components/contactBar/components/ContactSetting.vue';

const chatStore = ref({
  staffPlatformType: 1,
  isVirtualSeat: false,
  currentChat: {
    staffPlatformType: 1
  }
});

const props = defineProps<{
  item: Contact;
}>();

const contactItemClick = (item: Contact) => {
  console.log(item);
};

const accountEncrypt = (account: string) => {
  return account;
};
</script>

<style lang="scss" scoped>
.contact-item {
    @apply gap-2 mx-2 py-3 px-2 overflow-hidden cursor-pointer transition-all duration-300 rounded-lg;
  .new-tips {
    @apply px-1 py-1px text-center leading-4 text-xs text-white bg-green-500 rounded-full;
  }
  &:hover {
    @apply bg-#F0F2F5;
    .contact-message {
      padding-right: 25px;
      .contact-setting {
        transform: translateX(0) scale(1);
        opacity: 1;
        pointer-events: auto;
      }
    }
  }
  &.active {
    @apply bg-#f4f8f6;
  }
  & > * {
    flex-shrink: 0;
    line-height: 1;
  }
  .contact-content {
    @apply flex flex-col w-full flex-1 overflow-hidden;
  }
  .read-icon {
    @apply mr-1 flex-shrink-0 font-bold leading-none;
  }
  .contact-message {
    @apply text-sm text-gray-500 mt-1 flex items-center justify-between relative transition-all duration-300 overflow-hidden;
  }
}
</style>
