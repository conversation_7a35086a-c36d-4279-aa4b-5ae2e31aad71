import {useUserInfoStore} from './userInfo.ts'
import {getConfig, updateConfig} from '@/api/setting/index.ts';
import {SettingConfig} from '@/api/setting/types.ts'

export const useChatStore = defineStore('chat', () => {
    const userInfoStore = useUserInfoStore()
    // 全局开关
    const globalSwitch = ref({
        userId: userInfoStore.seatInfo?.id,
        receiveTranslationEnabled: 0,
        receiveTranslationConfig: {
            language: '',
            service: undefined
        },
        sendTranslationEnabled: 0,
        sendTranslationConfig: {
            language: '',
            service: undefined
        },
        voiceTranslationEnabled: 0,
        desktopNotificationEnabled: 0,
        keywordReplyEnabled: 0,
        autoReplyEnabled: 0,
        groupAutoTranslationEnabled: 0
    });
    // 获取全局开关
    const getGlobalSwitch = async () => {
        const res = await getConfig(userInfoStore.seatInfo.id);
        // 如果receiveTranslationConfig为空，则赋值为默认值
        if (!res.data.receiveTranslationConfig) {
            res.data.receiveTranslationConfig = {
                language: '',
                service: ''
            };
        }
        // 如果sendTranslationConfig为空，则赋值为默认值
        if (!res.data.sendTranslationConfig) {
            res.data.sendTranslationConfig = {
                language: '',
                service: ''
            };
        }
        globalSwitch.value = res.data;
    };
    // 更新全局开关
    const updateGlobalSwitch = async (data: SettingConfig) => {
        const res = await updateConfig({userId: userInfoStore.seatInfo.id, ...data});
        return res.data;
    };


    return {
        globalSwitch, // 全局开关
        getGlobalSwitch, // 获取全局开关
        updateGlobalSwitch, // 更新全局开关
    };
});
export default useChatStore;
