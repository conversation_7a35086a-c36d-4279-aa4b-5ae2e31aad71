<template>
  <CommonDrawer v-model="drawer" title="上传通讯录" @confirm="handleConfirm" @close="handleDrawerClose">
    <el-form class="ugly-form" ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="数据来源">
        <el-select class="ugly-select" popper-class="ugly-select-popper" v-model="form.type" placeholder="请选择数据来源" @change="handleTypeChange">
          <el-option label="主管数据" :value="1" />
          <el-option label="自定义数据" :value="2" />
          <el-option label="手动录入" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.type === 1" label="选择加粉数据" prop="daName">
        <SelectPage ref="selectPageRef" v-model="form.daName" :request-api="getAutoMessageList" typename="masspost" :props="{ label: 'dataName', value: 'dataName' }" placeholder="请选择ws粉丝数据" />
      </el-form-item>
      <el-form-item v-else-if="form.type === 2" label="上传加粉数据" prop="filePath">
        <el-upload :action="uploadFileUrl" :auto-upload="true" :show-file-list="false" accept=".txt" :on-success="handleUploadSuccess" :on-error="handleUploadError" :before-upload="beforeUpload" :headers="headers" class="w-full flex flex-col">
          <div v-if="!form.filePath" class="w-full flex items-center justify-between">
            <el-button type="primary" size="large" plain :icon="Upload" class="flex-1">上传数据</el-button>
            <el-button link plain :icon="Download" size="large" class="flex-shrink-0" @click.stop="downloadTemplate">下载模板</el-button>
          </div>
          <div v-else class="w-full flex items-center justify-between">
            <el-input v-model="fileName" placeholder="请选择文件" readonly class="w-full">
              <template #append>
                <el-button :icon="Upload" class="!text-#10b981" plain size="small"></el-button>
              </template>
            </el-input>
          </div>
        </el-upload>
        <p v-if="effectiveDataNumber && effectiveDataNumber >= 0" class="text-base text-gray-5 mt-2">
          有效数据数量：<span class="text-#10b981">{{ effectiveDataNumber }}</span>
        </p>
      </el-form-item>
      <el-form-item v-else-if="form.type === 3" label="录入加粉数据" prop="tempFansList">
        <el-input class="!text-18px" v-model="form.tempFansList" type="textarea" :rows="6" placeholder="每行一个手机号，请输入纯数字" />
        <p class="text-base text-gray mt-2">最多10条，超过10条请使用自定义数据上传</p>
      </el-form-item>
      <el-form-item v-if="form.type === 1 || form.type === 2" label="上传通讯录数量" prop="limit">
        <el-input-number v-model="form.limit" :min="1" :max="form.type === 1 ? 99999 : effectiveDataNumber || 1" placeholder="请输入上传通讯录数量" size="large" /><span class="ml-2 text-base text-gray-600">条数据</span>
      </el-form-item>
    </el-form>
  </CommonDrawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Download, Upload } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import type { UploadFile, FormRules } from 'element-plus';
const drawer = ref(false);
const loading = ref(false);

// 上传加粉数据
const fileName = ref(''); // 上传加粉数据文件名
const effectiveDataNumber = ref(null); //返回的有效数据数量
const formRef = ref();
const selectPageRef = ref();
const form = ref({
  account: '', //chatStore.currentChat.account,
  type: 1,
  daName: '',
  filePath: '',
  limit: 0,
  tempFansList: '',
  fansList: undefined
});
const validateFansList = (rule: any, value: any, callback: any) => {
  // 校验规则为：每行只可以是数字，且不能有空行，最多 10 行
  if (!value) {
    callback(new Error('请输入加粉数据'));
    return;
  }
  const lines = value.split('\n');
  if (lines.some((line) => line.trim() === '')) {
    callback(new Error('不可有空行'));
  }
  if (lines.length > 10) {
    callback(new Error('最多只能输入10行数据'));
  }
  for (const line of lines) {
    if (!/^\d+$/.test(line.trim())) {
      callback(new Error('号码请输入数字'));
    }
  }
  callback();
};
const rules: FormRules = {
  type: [{ required: true, message: '请选择数据来源', trigger: 'change' }],
  daName: [{ required: true, message: '请选择加粉数据', trigger: 'change' }],
  limit: [{ required: true, message: '请输入上传通讯录数量', trigger: 'blur' }],
  filePath: [{ required: true, message: '请上传有效数据的通讯录', trigger: 'change' }],
  tempFansList: [
    { required: true, message: '请输入加粉数据', trigger: 'blur' },
    { validator: validateFansList, trigger: 'blur' }
  ]
};
// 切换数据来源
const handleTypeChange = (type: number) => {
  if (type === 1) {
    form.value.limit = 1;
  } else {
    form.value.limit = effectiveDataNumber.value || 1;
  }
};
// 获取粉丝库数据
const getAutoMessageList = async (params: { page: number; pageSize: number }) => {
  try {
    const res = {
      rows: [],
      total: 0
    };
    // const res = await fansListApi({
    //   pageNum: params.page,
    //   pageSize: params.pageSize,
    //   existFans: 1,
    //   seatId: isStaff.value ? chatStore.currentChat.staffId : undefined
    // });
    return {
      list: res.rows || [],
      total: res.total || 0
    };
  } catch (error) {
    console.error('获取自动回复模板失败:', error);
    return {
      list: [],
      total: 0
    };
  }
};

// ==========以下为 上传加粉数据 相关方法==========
const uploadFileUrl = ''; //computed(() => useAppStore().apiBaseUrl + '/talk/message/upload/friends/check'); // 上传文件服务器地址
const headers = {
  Authorization: 'Bearer xxxxxxxxxxxx'
}; //ref(globalHeaders());
const handleUploadSuccess = (response: any, uploadFile: UploadFile) => {
  if (response.code === 200) {
    form.value.filePath = response.data.url;
    effectiveDataNumber.value = response.data.effectiveDataNumber || 0;
    fileName.value = response.data.fileName;
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};
const handleUploadError = () => {
  ElMessage.error('上传失败');
};
const beforeUpload = (file: File) => {
  const isExcel = /\.(txt)$/.test(file.name.toLowerCase());
  if (!isExcel) {
    ElMessage.error('只能上传 TXT 文件!');
    return false;
  }
  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    ElMessage.error('文件大小不能超过 1MB!');
    return false;
  }
  return true;
};
// 下载excel模板
const templateUrl = 'https://a2c-static.oss-cn-hongkong.aliyuncs.com/templates/contact-template.txt';
const downloadTemplate = () => {
  const link = document.createElement('a');
  link.href = templateUrl;
  link.download = '通讯录导入模板.txt';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// ==========以下为 drawer 相关方法==========
const handleConfirm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 手动录入
      if (form.value.type === 3) {
        form.value.limit = 10;
        form.value.filePath = undefined;
        form.value.daName = undefined;
        form.value.fansList = form.value.tempFansList.replace(/\n/g, ',').split(',');
      } else {
        form.value.fansList = undefined;
      }
      if (effectiveDataNumber.value == 0) {
        ElMessage.error('上传文件无有效数据，请重新上传');
        return;
      }
      form.value.account = '123'; //chatStore.currentChat.account;
      // 删除tempFansList
      const { tempFansList, ...rest } = form.value;
      loading.value = true;
      //   uploadContact({ seatAccount: chatStore.currentChat.staffAccount, detailBoList: [rest] })
      //     .then((res) => {
      //       if (res.code === 200) {
      //         ElMessage.success('上传成功');
      //         drawer.value = false;
      //         // 刷新联系人列表
      //         chatStore.updateContactQuery({ pageNum: 1, refreshContact: true, friendAccount: '', type: 1 });
      //       }
      //     })
      //     .finally(() => {
      //       loading.value = false;
      //     });
    }
  });
};

const handleDrawerClose = () => {
  // 处理drawer关闭事件
  console.log('Drawer已关闭');
};

const open = () => {
  drawer.value = true;
  effectiveDataNumber.value = null;
  fileName.value = '';
  selectPageRef.value?.refresh(); // 刷新加粉数据下拉
  form.value = {
    account: '123', //chatStore.currentChat.account,
    type: 1,
    daName: '',
    limit: 0,
    filePath: '',
    tempFansList: '',
    fansList: undefined
  };
};
defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.ugly-form {
  :deep(.el-form-item) {
    margin-bottom: 50px;
  }
  :deep(.el-form-item__label) {
    color: #008069;
    font-size: 17px;
    font-weight: 500;
    margin-bottom: 20px;
    margin-top: 10px;
  }
  :deep(.el-textarea__inner) {
    border: 2px solid #d1d7db;
  }
}
</style>
