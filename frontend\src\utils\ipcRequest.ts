import {ipc} from './ipcRenderer';
import {ipcApiRoute} from '@/api';
import {ElMessage} from 'element-plus';
import {useAppStore} from '@/stores/modules/app';
import {isHttp} from './validate';
import {useUserInfoStore} from '@/stores/modules/userInfo'

// 获取token的函数，优先从stores获取，回退到localStorage
const getToken = (): string => {
    try {
        // 检查是否在浏览器环境且Pinia已初始化
        if (typeof window !== 'undefined' && (window as any).__PINIA__) {
            // 延迟导入避免循环依赖

            const userStore = useUserInfoStore();
            const token = userStore.accessToken;
            if (token) {
                return token;
            }
        }
    } catch (error) {
        // 如果stores获取失败，回退到localStorage
        console.warn('从stores获取token失败，回退到localStorage:', error);
    }

    // 回退到localStorage
    return localStorage.getItem('access_token') || '';
};

// 定义请求配置类型
interface IpcRequestOptions {
    url: string;
    method?: 'get' | 'post' | 'put' | 'delete' | 'patch';
    params?: Record<string, unknown>;
    data?: Record<string, unknown>;
    headers?: Record<string, string>;
    timeout?: number;
    showErrorMessage?: boolean; // 是否显示错误提示，默认为true
    allowConcurrent?: boolean; // 是否允许并发请求，默认为false（即默认启用请求去重）
    cacheTimeout?: number; // 请求缓存时间，默认为5000ms（5秒）
}

// 请求缓存管理
interface IpcRequestCache {
    timestamp: number;
    promise: Promise<unknown>;
    timeout: number;
}

// 请求缓存存储
const ipcRequestCache = new Map<string, IpcRequestCache>();

// 设备ID缓存
let cachedDeviceId: string | null = null;

// 防止重复跳转到登录页的标志
let isRedirectingToLogin = false;

/**
 * 清理不可克隆的对象，解决 "An object could not be cloned" 错误
 * @param obj 需要清理的对象
 * @param visited 已访问的对象集合，用于处理循环引用
 * @returns 清理后的对象
 */
const sanitizeForIpc = (obj: any, visited = new WeakSet()): any => {
    // 处理 null 和 undefined
    if (obj === null || obj === undefined) {
        return obj;
    }

    // 处理基本类型
    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') {
        return obj;
    }

    // 处理函数 - 移除函数
    if (typeof obj === 'function') {
        return undefined;
    }

    // 处理 Symbol - 转换为字符串
    if (typeof obj === 'symbol') {
        return obj.toString();
    }

    // 处理 BigInt - 转换为字符串
    if (typeof obj === 'bigint') {
        return obj.toString();
    }

    // 处理 Date
    if (obj instanceof Date) {
        return obj.toISOString();
    }

    // 处理 RegExp
    if (obj instanceof RegExp) {
        return obj.toString();
    }

    // 处理 DOM 元素和 Node - 移除
    if (typeof window !== 'undefined' && (
        obj instanceof Element ||
        obj instanceof Node ||
        obj instanceof Event ||
        obj instanceof FileList ||
        obj instanceof File
    )) {
        return undefined;
    }

    // 处理循环引用
    if (visited.has(obj)) {
        return '[Circular Reference]';
    }

    // 处理数组
    if (Array.isArray(obj)) {
        visited.add(obj);
        const sanitizedArray = obj.map(item => sanitizeForIpc(item, visited)).filter(item => item !== undefined);
        visited.delete(obj);
        return sanitizedArray;
    }

    // 处理普通对象
    if (obj !== null && typeof obj === 'object') {
        // 检查是否是纯对象或特殊对象
        const isPlainObject = obj.constructor === Object || obj.constructor === undefined;

        if (!isPlainObject) {
            // 对于非纯对象，尝试提取其可序列化的属性
            try {
                JSON.stringify(obj);
            } catch (error) {
                // 如果无法序列化，返回对象的类型信息
                return `[${obj.constructor?.name || 'Object'}]`;
            }
        }

        visited.add(obj);
        const sanitizedObj: any = {};

        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const sanitizedValue = sanitizeForIpc(obj[key], visited);
                if (sanitizedValue !== undefined) {
                    sanitizedObj[key] = sanitizedValue;
                }
            }
        }

        visited.delete(obj);
        return sanitizedObj;
    }

    return obj;
};

// 获取设备唯一ID
const getDeviceId = async (): Promise<string> => {
    // 如果已经缓存了设备ID，直接返回
    if (cachedDeviceId) {
        return cachedDeviceId;
    }

    try {
        const systemInfo = await ipc.invoke(ipcApiRoute.getSystemInfo, {});
        cachedDeviceId = systemInfo?.machineCode || '';
        console.log('获取设备ID:', cachedDeviceId);
        return cachedDeviceId;
    } catch (error) {
        console.error('获取设备ID失败:', error);
        return '';
    }
};

// 生成请求唯一标识
const generateIpcRequestKey = (options: IpcRequestOptions): string => {
    const {url, method, params, data} = options;
    const key = {
        url,
        method,
        params: params || {},
        data: data || {}
    };
    return JSON.stringify(key);
};

// 清理过期的请求缓存
const cleanExpiredIpcCache = () => {
    const now = Date.now();
    for (const [key, cache] of ipcRequestCache.entries()) {
        if (now - cache.timestamp > cache.timeout) {
            ipcRequestCache.delete(key);
        }
    }
};

// 获取动态的 baseURL
const getIpcBaseURL = (): string => {
    try {
        const appStore = useAppStore();
        let baseUrl = appStore.apiBaseUrl || import.meta.env.VITE_APP_BASE_API || '';

        // 开发环境下，如果是相对路径，则使用当前域名
        if (import.meta.env.DEV && !isHttp(baseUrl)) {
            baseUrl = window.location.origin + baseUrl;
        }

        console.log('IPC请求使用的baseURL:', baseUrl);
        return baseUrl;
    } catch (error) {
        // 如果 store 还未初始化，使用环境变量
        let apiLink = import.meta.env.VITE_APP_BASE_API || '';
        if (!isHttp(apiLink)) {
            apiLink = window.location.origin + apiLink;
        }
        console.log('IPC请求fallback baseURL:', apiLink);
        return apiLink;
    }
};

// 处理完整URL
const processUrl = (url: string): string => {
    // 如果已经是完整URL，直接返回
    if (isHttp(url)) {
        return url;
    }

    // 如果是相对路径，拼接baseURL
    const baseUrl = getIpcBaseURL();
    return baseUrl + (url.startsWith('/') ? url : '/' + url);
};

/**
 * 通过IPC代理HTTP请求，解决CORS问题
 */
const ipcRequest = async <T = unknown>(options: IpcRequestOptions): Promise<T> => {
    const {
        method = 'get',
        url,
        params,
        data,
        headers = {},
        timeout = 15000,
        showErrorMessage = true,
        allowConcurrent = false,
        cacheTimeout = 5000
    } = options;

    // 处理URL
    const fullUrl = processUrl(url);

    // 添加认证头
    const requestHeaders = {...headers};
    const token = getToken();
    const clientid = import.meta.env.VITE_APP_CLIENT_ID;

    if (clientid) {
        requestHeaders['clientid'] = clientid;
    }
    if (token) {
        requestHeaders.Authorization = `Bearer ${token}`;
    }

    // 添加设备ID到请求头
    try {
        const deviceId = await getDeviceId();
        if (deviceId) {
            requestHeaders['deviceId'] = deviceId;
        }
    } catch (error) {
        console.warn('添加设备ID到请求头失败:', error);
        // 即使获取设备ID失败，也继续执行请求
    }
    // 生成请求唯一标识
    const requestOptions = {...options, url: fullUrl, headers: requestHeaders};
    const requestKey = generateIpcRequestKey(requestOptions);

    // 清理过期缓存
    cleanExpiredIpcCache();

    // 如果不允许并发且存在相同的请求
    if (!allowConcurrent && ipcRequestCache.has(requestKey)) {
        const existingCache = ipcRequestCache.get(requestKey)!;
        console.log(`返回缓存的IPC请求: ${method.toUpperCase()} ${fullUrl}`);
        return existingCache.promise as Promise<T>;
    }

    // 调试日志
    console.log('IPC代理请求URL:', fullUrl);
    console.log('IPC代理请求方法:', method);
    console.log('IPC代理请求参数:', params || data || {});

    const requestPromise = new Promise<T>(async (resolve, reject) => {
        try {
            // 清理不可克隆的对象，避免 "An object could not be cloned" 错误
            const sanitizedParams = sanitizeForIpc(params);
            const sanitizedData = sanitizeForIpc(data);
            const sanitizedHeaders = sanitizeForIpc(requestHeaders);

            // 在开发环境下，如果数据被清理了，输出警告信息
            if (import.meta.env.DEV) {
                if (JSON.stringify(params) !== JSON.stringify(sanitizedParams)) {
                    console.warn('IPC请求参数中包含不可克隆对象，已进行清理:', {
                        original: params,
                        sanitized: sanitizedParams
                    });
                }
                if (JSON.stringify(data) !== JSON.stringify(sanitizedData)) {
                    console.warn('IPC请求数据中包含不可克隆对象，已进行清理:', {
                        original: data,
                        sanitized: sanitizedData
                    });
                }
            }

            const response = await ipc.invoke(ipcApiRoute.httpRequest, {
                url: fullUrl,
                method: method.toUpperCase(),
                params: sanitizedParams,
                data: sanitizedData,
                headers: sanitizedHeaders,
                timeout
            });

            console.log('IPC代理响应:', response);

            // 请求成功后清理缓存
            ipcRequestCache.delete(requestKey);

            if (response.status) {
                // 检查响应数据是否包含API错误码
                const responseData = response.data;
                if (responseData && typeof responseData === 'object' && 'code' in responseData && typeof responseData.code === 'number' && responseData.code !== 200 && responseData.code !== 0) {
                    // 错误信息优先取msg，其次取message
                    const errorMsg = (responseData.msg || responseData.message || '未知错误') as string;

                    // 显示错误提示
                    if (showErrorMessage) {
                        ElMessage.error(errorMsg);
                    }

                    // 处理特定错误码
                    if (responseData.code === 401) {
                        console.error('未授权，请重新登录');
                        // 可以在这里添加登出和重定向逻辑
                    } else if (responseData.code === 460 || responseData.code === 461) {
                        console.error('Token已失效，需要重新登录');

                        // 防止重复跳转
                        if (isRedirectingToLogin) {
                            console.log('已在跳转到登录页面，跳过重复处理');
                            reject(new Error('Token已失效'));
                            return;
                        }
                        isRedirectingToLogin = true;

                        // 立即同步清除localStorage中的token，确保路由守卫能够立即检测到
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('refresh_token');
                        localStorage.removeItem('token_expire_time');
                        localStorage.removeItem('refresh_expire_time');

                        try {
                            // 动态导入store和router，避免循环依赖
                            const {useUserInfoStore} = await import('@/stores/modules/userInfo');
                            
                            // 清除store中的所有token和用户信息
                            const userStore = useUserInfoStore();
                            userStore.clearAll();

                            // 使用window.location直接跳转，避免Router实例问题
                            window.location.hash = '#/login';
                            
                            console.log('已清除所有token并跳转到登录页面');
                            
                            // 1秒后重置标志，允许后续可能的跳转
                            setTimeout(() => {
                                isRedirectingToLogin = false;
                            }, 1000);
                        } catch (error) {
                            console.error('处理460/461错误时发生异常:', error);
                            // 如果store/router获取失败，直接清除localStorage并强制跳转
                            window.location.hash = '#/login';
                            // 可选：刷新页面确保状态完全重置
                            setTimeout(() => {
                                window.location.reload();
                            }, 100);
                            
                            // 重置跳转标志
                            setTimeout(() => {
                                isRedirectingToLogin = false;
                            }, 1000);
                        }
                    }

                    reject(new Error(errorMsg));
                } else {
                    // 直接返回整个响应数据
                    resolve(response.data as T);
                }
            } else {
                // 统一处理网络错误
                let errorMsg = '网络请求失败';

                if (response.statusCode) {
                    // 根据状态码处理错误
                    switch (response.statusCode) {
                        case 400:
                            errorMsg = '请求错误';
                            break;
                        case 401:
                            errorMsg = '未授权，请重新登录';
                            break;
                        case 403:
                            errorMsg = '拒绝访问';
                            break;
                        case 404:
                            errorMsg = '请求的资源不存在';
                            break;
                        case 500:
                            // 尝试从后端响应中提取错误信息
                            if (response.data) {
                                // 优先取msg，其次取message，最后使用默认信息
                                errorMsg = response.data.msg || response.data.message || '服务器内部错误';
                            } else {
                                errorMsg = '服务器内部错误';
                            }
                            break;
                        default:
                            errorMsg = response.message || `请求错误: ${response.statusCode}`;
                    }
                } else {
                    errorMsg = response.message || '请求失败';
                }

                // 显示错误提示
                if (showErrorMessage) {
                    ElMessage.error(errorMsg);
                }

                console.error('IPC代理请求错误:', errorMsg);
                reject(new Error(errorMsg));
            }
        } catch (error) {
            // 请求完成后清理缓存
            ipcRequestCache.delete(requestKey);

            console.error('IPC代理请求异常:', error);

            const errorMsg = error instanceof Error ? error.message : '网络请求失败';
            if (showErrorMessage) {
                ElMessage.error(errorMsg);
            }
            reject(error);
        }
    });

    // 如果不允许并发，将请求加入缓存
    if (!allowConcurrent) {
        ipcRequestCache.set(requestKey, {
            timestamp: Date.now(),
            promise: requestPromise,
            timeout: cacheTimeout
        });
    }

    return requestPromise;
};

export default ipcRequest;

// 导出数据清理函数，供其他地方使用
export {sanitizeForIpc};
