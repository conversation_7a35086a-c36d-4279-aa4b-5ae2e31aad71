'use strict';
const {machineIdSync} = require('node-machine-id');
const {logger} = require('ee-core/log');
const os = require('os')
const {app, BrowserWindow, dialog, Tray, Menu, nativeImage} = require('electron')
const {post, get} = require("axios");
const {timestampToString} = require("../utils/CommonUtils");
const path = require('path');
const fs = require('fs');

class SystemService {

    async getBaseInfo(args, event) {
        // 获取基于硬件的原始机器码（更难伪造）
        const hardwareId = machineIdSync(true);
        // 获取操作系统信息
        const systemInfo = {
            name: app.getName(),
            platform: process.platform, // 操作系统类型
            version: app.getVersion(),
            osName: this.getOSName(),        // 操作系统名称
            arch: os.arch(),            // 系统架构
            machineCode: hardwareId //机器码
        }
        return systemInfo;
    }

//   * 错误码说明：
// *  {401} 参数缺失 - 请求缺少必要参数或参数格式错误
// *  {402} 账户不存在 - 提供的凭证未关联任何注册账户
// *  {403} 账户禁用 - 账户已被系统管理员停用
// *  {404} 设备授权过期 - 当前设备授权已超过有效期
// *  {405} 设备禁用 - 账户已主动禁用该设备访问权限
    async login(args, event) {
        const {authKey} = args;
        const machineCode = machineIdSync(true);
        // 参数校验：邀请码不能为空
        if (!authKey) return {status: false, message: 'login.errors.emptyAuthCode'};
        try {
            // 统一转换为小写比较
            const lowerCaseCode = machineCode.toLowerCase();
            const url = app.baseUrl + '/check_login'
            const reqData = {key: authKey, device: lowerCaseCode}
            const res = await post(url, reqData, {timeout: 30000})
            const {code, message, device_valid_until, user_remaining_chars, device_status, user_name} = res.data
            // logger.info('响应数据======：',res.data)
            // 根据响应code处理不同情况
            switch (code) {
                case 2000: // 请求成功
                    const timestamp = device_valid_until

                    const data = {
                        expireTime: timestampToString(timestamp),//失效时间
                        totalChars: user_remaining_chars,//剩余字符数
                        authKey: authKey,//授权码
                        machineCode: machineCode,//机器码
                        userName: user_name//用户名
                    }
                    app.authInfo = data;
                    return {status: true, message: 'login.success', data: data};

                case 401: // 参数缺失 - 请求缺少必要参数或参数格式错误
                    return {status: false, message: 'login.errors.invalidParams'};

                case 402: // 账户不存在 - 提供的凭证未关联任何注册账户
                    return {status: false, message: 'login.errors.accountNotExist'};

                case 403: // 账户禁用 - 账户已被系统管理员停用
                    return {status: false, message: 'login.errors.accountDisabled'};

                case 404: // 设备授权过期 - 当前设备授权已超过有效期
                    return {status: false, message: 'login.errors.expired'};

                case 405: // 设备禁用 - 账户已主动禁用该设备访问权限
                    return {status: false, message: 'login.errors.deviceDisabled'};

                default: // 其他未知错误
                    return {status: false, message: 'login.errors.unknown'};
            }
        } catch (err) {
            // app.authInfo = null;
            logger.error('login request error:', err.message);
            // 网络请求异常
            return {
                status: false,

                message: 'login.errors.networkError'
            };
        }
    }

    getOSName() {
        switch (process.platform) {
            case 'win32':
                return 'Windows'
            case 'darwin':
                return 'macOS'
            case 'linux':
                return 'Linux'
            default:
                return 'Unknown'
        }
    }

    // 获取系统设置
    async getSystemSettings(args, event) {
        try {
            const settings = this.loadSettings();
            const cacheDir = await this.getCacheDirectory();

            return {
                status: true,
                data: {
                    ...settings,
                    cacheDirectory: cacheDir
                }
            };
        } catch (error) {
            logger.error('获取系统设置失败:', error);
            return {
                status: false,
                message: '获取系统设置失败'
            };
        }
    }

    // 更新系统设置
    async updateSystemSettings(args, event) {
        try {
            const {key, value} = args;
            const settings = this.loadSettings();

            // 更新设置
            settings[key] = value;

            // 保存设置
            this.saveSettings(settings);

            // 应用设置
            await this.applySettings(key, value);

            return {
                status: true,
                message: '设置更新成功'
            };
        } catch (error) {
            logger.error('更新系统设置失败:', error);
            return {
                status: false,
                message: '更新系统设置失败'
            };
        }
    }

    // 选择缓存目录
    async selectCacheDirectory(args, event) {
        try {
            const result = await dialog.showOpenDialog({
                properties: ['openDirectory'],
                title: '选择缓存目录'
            });

            if (!result.canceled && result.filePaths.length > 0) {
                const selectedPath = result.filePaths[0];

                // 更新缓存目录设置
                const settings = this.loadSettings();
                settings.cacheDirectory = selectedPath;
                this.saveSettings(settings);

                return {
                    status: true,
                    data: selectedPath,
                    message: '缓存目录设置成功，重启后生效'
                };
            }

            return {
                status: false,
                message: '未选择目录'
            };
        } catch (error) {
            logger.error('选择缓存目录失败:', error);
            return {
                status: false,
                message: '选择缓存目录失败'
            };
        }
    }

    // 获取当前缓存目录
    async getCacheDirectory(args, event) {
        try {
            const settings = this.loadSettings();
            const defaultCacheDir = path.join(app.getPath('userData'), 'cache');
            const cacheDir = settings.cacheDirectory || defaultCacheDir;

            return {
                status: true,
                data: cacheDir
            };
        } catch (error) {
            logger.error('获取缓存目录失败:', error);
            return {
                status: false,
                message: '获取缓存目录失败'
            };
        }
    }

    // 获取缓存信息（包括大小）
    async getCacheInfo(args, event) {
        try {
            const settings = this.loadSettings();
            const defaultCacheDir = path.join(app.getPath('userData'), 'cache');
            const cacheDir = settings.cacheDirectory || defaultCacheDir;

            // 确保缓存目录存在
            if (!fs.existsSync(cacheDir)) {
                fs.mkdirSync(cacheDir, {recursive: true});
            }

            // 计算缓存目录大小
            const cacheSize = await this.calculateDirectorySize(cacheDir);

            return {
                status: true,
                data: {
                    cacheDirectory: cacheDir,
                    cacheSize: cacheSize,
                    cacheSizeFormatted: this.formatBytes(cacheSize)
                }
            };
        } catch (error) {
            logger.error('获取缓存信息失败:', error);
            return {
                status: false,
                message: '获取缓存信息失败'
            };
        }
    }

    // 清理缓存文件
    async clearCache(args, event) {
        try {
            const settings = this.loadSettings();
            const defaultCacheDir = path.join(app.getPath('userData'), 'cache');
            const cacheDir = settings.cacheDirectory || defaultCacheDir;

            let clearedSize = 0;
            let clearedFiles = 0;

            // 清理文件系统缓存
            if (fs.existsSync(cacheDir)) {
                const result = await this.clearDirectory(cacheDir);
                clearedSize = result.size;
                clearedFiles = result.files;
            }

            // 清理Electron会话缓存
            await this.clearElectronCache();

            return {
                status: true,
                message: '缓存文件清理成功',
                data: {
                    clearedSize: clearedSize,
                    clearedSizeFormatted: this.formatBytes(clearedSize),
                    clearedFiles: clearedFiles
                }
            };
        } catch (error) {
            logger.error('清理缓存文件失败:', error);
            return {
                status: false,
                message: '清理缓存文件失败'
            };
        }
    }

    // 清理数据库缓存（独立方法，按需使用）
    async clearDatabaseCache(args, event) {
        try {
            let clearedDbRecords = 0;

            // 清理数据库翻译缓存
            const translateCacheCount = await app.sdb.count('translate_cache');
            if (translateCacheCount > 0) {
                clearedDbRecords = await app.sdb.delete('translate_cache');
            }

            return {
                status: true,
                message: '数据库缓存清理成功',
                data: {
                    clearedDbRecords: clearedDbRecords
                }
            };
        } catch (error) {
            logger.error('清理数据库缓存失败:', error);
            return {
                status: false,
                message: '清理数据库缓存失败'
            };
        }
    }

    // 清理所有缓存（文件 + 数据库）
    async clearAllCache(args, event) {
        try {
            // 清理文件缓存
            const fileResult = await this.clearCache(args, event);
            if (!fileResult.status) {
                return fileResult;
            }

            // 清理数据库缓存
            const dbResult = await this.clearDatabaseCache(args, event);
            if (!dbResult.status) {
                return dbResult;
            }

            return {
                status: true,
                message: '所有缓存清理成功',
                data: {
                    clearedSize: fileResult.data.clearedSize,
                    clearedSizeFormatted: fileResult.data.clearedSizeFormatted,
                    clearedFiles: fileResult.data.clearedFiles,
                    clearedDbRecords: dbResult.data.clearedDbRecords
                }
            };
        } catch (error) {
            logger.error('清理所有缓存失败:', error);
            return {
                status: false,
                message: '清理所有缓存失败'
            };
        }
    }

    // 计算目录大小
    async calculateDirectorySize(dirPath) {
        let totalSize = 0;

        const calculateSize = async (currentPath) => {
            try {
                const stats = await fs.promises.stat(currentPath);

                if (stats.isFile()) {
                    totalSize += stats.size;
                } else if (stats.isDirectory()) {
                    const files = await fs.promises.readdir(currentPath);
                    for (const file of files) {
                        await calculateSize(path.join(currentPath, file));
                    }
                }
            } catch (error) {
                // 忽略无法访问的文件/目录
                logger.warn(`无法访问文件: ${currentPath}`, error.message);
            }
        };

        await calculateSize(dirPath);
        return totalSize;
    }

    // 清理目录
    async clearDirectory(dirPath) {
        let clearedSize = 0;
        let clearedFiles = 0;

        const clearDir = async (currentPath) => {
            try {
                const files = await fs.promises.readdir(currentPath);

                for (const file of files) {
                    const filePath = path.join(currentPath, file);
                    const stats = await fs.promises.stat(filePath);

                    if (stats.isFile()) {
                        clearedSize += stats.size;
                        clearedFiles++;
                        await fs.promises.unlink(filePath);
                    } else if (stats.isDirectory()) {
                        await clearDir(filePath);
                        await fs.promises.rmdir(filePath);
                    }
                }
            } catch (error) {
                logger.warn(`清理目录失败: ${currentPath}`, error.message);
            }
        };

        await clearDir(dirPath);
        return {size: clearedSize, files: clearedFiles};
    }

    // 清理Electron会话缓存
    async clearElectronCache() {
        try {
            const {session} = require('electron');

            // 清理默认会话缓存
            await session.defaultSession.clearCache();
            await session.defaultSession.clearStorageData();

            // 清理所有分区会话缓存
            if (app.viewsMap) {
                for (const [partitionId, view] of app.viewsMap) {
                    if (view && view.webContents && !view.webContents.isDestroyed()) {
                        try {
                            await view.webContents.session.clearCache();
                            await view.webContents.session.clearStorageData();
                        } catch (error) {
                            logger.warn(`清理会话缓存失败 ${partitionId}:`, error.message);
                        }
                    }
                }
            }
        } catch (error) {
            logger.warn('清理Electron缓存失败:', error.message);
        }
    }

    // 格式化字节大小
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 加载设置
    loadSettings() {
        try {
            const settingsPath = path.join(app.getPath('userData'), 'system-settings.json');
            if (fs.existsSync(settingsPath)) {
                const data = fs.readFileSync(settingsPath, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            logger.error('加载设置失败:', error);
        }

        // 返回默认设置
        return {
            powerBoot: false,
            minimizeToTray: false,
            trayMessage: false,
            messagePrompt: true,
            promptSound: true,
            pageZoom: false,
            cacheDirectory: null
        };
    }

    // 保存设置
    saveSettings(settings) {
        try {
            const settingsPath = path.join(app.getPath('userData'), 'system-settings.json');
            fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
        } catch (error) {
            logger.error('保存设置失败:', error);
            throw error;
        }
    }

    // 应用设置
    async applySettings(key, value) {
        try {
            switch (key) {
                case 'powerBoot':
                    this.setAutoLaunch(value);
                    break;
                case 'minimizeToTray':
                    this.setMinimizeToTray(value);
                    break;
                case 'trayMessage':
                    this.setTrayMessage(value);
                    break;
                case 'messagePrompt':
                    this.setMessagePrompt(value);
                    break;
                case 'promptSound':
                    this.setPromptSound(value);
                    break;
                case 'pageZoom':
                    this.setPageZoom(value);
                    break;
            }
        } catch (error) {
            logger.error('应用设置失败:', error);
            throw error;
        }
    }

    // 设置开机自启
    setAutoLaunch(enabled) {
        try {
            app.setLoginItemSettings({
                openAtLogin: enabled,
                openAsHidden: false
            });
            logger.info(`开机自启设置为: ${enabled}`);
        } catch (error) {
            logger.error('设置开机自启失败:', error);
            throw error;
        }
    }

    // 设置最小化到托盘
    setMinimizeToTray(enabled) {
        // 这个设置会在窗口关闭事件中使用
        app.minimizeToTray = enabled;
        logger.info(`最小化到托盘设置为: ${enabled}`);
    }

    // 设置托盘消息显示
    setTrayMessage(enabled) {
        app.trayMessage = enabled;
        logger.info(`托盘消息显示设置为: ${enabled}`);
    }

    // 设置消息提示
    setMessagePrompt(enabled) {
        app.messagePrompt = enabled;
        logger.info(`消息提示设置为: ${enabled}`);
    }

    // 设置提示声音
    setPromptSound(enabled) {
        app.promptSound = enabled;
        logger.info(`提示声音设置为: ${enabled}`);
    }

    // 设置页面缩放
    setPageZoom(enabled) {
        app.pageZoom = enabled;
        logger.info(`页面缩放设置为: ${enabled}`);

        // 应用缩放设置到所有现有的webContents
        this.applyZoomSettingToAllViews(enabled);
    }

    // 应用缩放设置到所有视图
    applyZoomSettingToAllViews(enabled) {
        try {
            const {getMainWindow} = require('ee-core/electron');
            const mainWindow = getMainWindow();

            if (mainWindow && mainWindow.webContents) {
                // 设置主窗口的缩放
                this.setWebContentsZoom(mainWindow.webContents, enabled);
            }

            // 设置所有子视图的缩放
            if (app.viewsMap) {
                app.viewsMap.forEach((view, partitionId) => {
                    if (view && view.webContents && !view.webContents.isDestroyed()) {
                        this.setWebContentsZoom(view.webContents, enabled);
                    }
                });
            }
        } catch (error) {
            logger.error('应用缩放设置失败:', error);
        }
    }

    // 设置单个webContents的缩放
    setWebContentsZoom(webContents, enabled) {
        try {
            if (!webContents || webContents.isDestroyed()) {
                return;
            }

            if (enabled) {
                // 启用缩放 - 移除缩放限制
                webContents.setZoomFactor(1.0);
                // 移除之前可能设置的事件监听器
                webContents.removeAllListeners('zoom-changed');
                webContents.removeAllListeners('before-input-event');

                logger.info('页面缩放已启用');
            } else {
                // 禁用缩放 - 固定缩放比例为1.0并阻止缩放事件
                webContents.setZoomFactor(1.0);

                // 移除之前的监听器，避免重复添加
                webContents.removeAllListeners('zoom-changed');
                webContents.removeAllListeners('before-input-event');

                // 监听缩放变化事件，强制重置为1.0
                webContents.on('zoom-changed', (event, zoomDirection) => {
                    // 阻止缩放，始终保持1.0
                    setTimeout(() => {
                        if (!webContents.isDestroyed()) {
                            webContents.setZoomFactor(1.0);
                        }
                    }, 0);
                });

                // 监听键盘事件来阻止缩放快捷键
                webContents.on('before-input-event', (event, input) => {
                    // 阻止Ctrl+滚轮、Ctrl++、Ctrl+-等缩放快捷键
                    if ((input.control || input.meta) && input.type === 'keyDown') {
                        if (input.key === '+' || input.key === '=' || input.key === '-' || input.key === '0') {
                            event.preventDefault();
                        }
                    }
                });

                // 注入CSS来禁用触摸缩放和其他缩放方式
                webContents.insertCSS(`
          html, body {
            zoom: 1 !important;
            transform: scale(1) !important;
            transform-origin: 0 0 !important;
          }
          * {
            zoom: 1 !important;
          }
        `).catch(() => {
                    // 忽略CSS注入错误
                });

                logger.info('页面缩放已禁用');
            }
        } catch (error) {
            logger.error('设置webContents缩放失败:', error);
        }
    }

    /**
     * HTTP请求代理 - 解决前端CORS问题
     */
    async httpRequest(args, event) {
        const {url, method = 'GET', data, params, headers = {}, timeout = 10000} = args;

        try {
            logger.info(`HTTP代理请求: ${method} ${url}`);
            logger.info(`请求参数:`, params || data || {});

            let response;
            const config = {
                headers: {
                    'User-Agent': 'A2C-Client/1.0.0',
                    'Content-Type': 'application/json',
                    ...headers
                },
                timeout,
                withCredentials: true // 允许跨域携带cookie
            };
            logger.info(`请求配置:`, config);
            // 支持更多HTTP方法
            const methodLower = method.toLowerCase();
            switch (methodLower) {
                case 'get':
                    response = await get(url, {params, ...config});
                    break;
                case 'post':
                    response = await post(url, data, {params, ...config});
                    break;
                case 'put':
                    // 需要导入axios实例来支持PUT方法
                    const axios = require('axios');
                    response = await axios.put(url, data, {params, ...config});
                    break;
                case 'delete':
                    const axiosDelete = require('axios');
                    response = await axiosDelete.delete(url, {params, data, ...config});
                    break;
                case 'patch':
                    const axiosPatch = require('axios');
                    response = await axiosPatch.patch(url, data, {params, ...config});
                    break;
                default:
                    throw new Error(`不支持的HTTP方法: ${method}`);
            }

            logger.info(`HTTP代理响应状态: ${response.status}`);
            logger.info(`HTTP代理响应数据:`, response.data);

            return {
                status: true,
                data: response.data,
                statusCode: response.status,
                headers: response.headers
            };
        } catch (error) {
            logger.error(`HTTP代理请求失败: ${error.message}`);

            // 详细的错误信息记录
            if (error.response) {
                logger.error(`错误状态码: ${error.response.status}`);
                logger.error(`错误响应数据:`, error.response.data);
            }

            return {
                status: false,
                message: error.message,
                statusCode: error.response?.status || 0,
                data: error.response?.data || null,
                headers: error.response?.headers || {}
            };
        }
    }

    /**
     * 本地代理连接测试
     */
    async testProxyConnection(args, event) {
        const {host, port, ipMold, userId, password, enableAuth} = args;
        logger.info('代理测试请求参数:', JSON.stringify({
            host,
            port,
            ipMold,
            userId: userId ? '***' : undefined,
            password: password ? '***' : undefined,
            enableAuth
        }));

        if (!host || !port) {
            return {
                status: false,
                message: '请提供代理服务器地址和端口'
            };
        }

        try {
            logger.info(`开始测试代理连接: ${ipMold}://${host}:${port}`);

            const axios = require('axios');
            const {HttpsProxyAgent} = require('https-proxy-agent');
            const {SocksProxyAgent} = require('socks-proxy-agent');

            let proxyAgent;
            let proxyUrl;

            // 测试直连是否可用
            try {
                logger.info('测试直连...');
                const directResponse = await axios.get('https://httpbin.org/ip', {
                    timeout: 5000,
                    validateStatus: () => true
                });
                logger.info(`直连测试结果: ${directResponse.status}`);
            } catch (error) {
                logger.warn(`直连测试失败: ${error.message}`);
            }

            // 构建代理URL - 修复认证逻辑
            const shouldUseAuth = enableAuth && userId && password;

            if (ipMold === 'http') {
                if (shouldUseAuth) {
                    // URL编码用户名和密码，防止特殊字符问题
                    const encodedUser = encodeURIComponent(userId);
                    const encodedPass = encodeURIComponent(password);
                    proxyUrl = `http://${encodedUser}:${encodedPass}@${host}:${port}`;
                    logger.info(`使用HTTP代理(带认证): ${host}:${port}`);
                } else {
                    proxyUrl = `http://${host}:${port}`;
                    logger.info(`使用HTTP代理(无认证): ${host}:${port}`);
                }
                proxyAgent = new HttpsProxyAgent(proxyUrl);
            } else if (ipMold === 'socks5') {
                if (shouldUseAuth) {
                    // URL编码用户名和密码，防止特殊字符问题
                    const encodedUser = encodeURIComponent(userId);
                    const encodedPass = encodeURIComponent(password);
                    proxyUrl = `socks5h://${encodedUser}:${encodedPass}@${host}:${port}`;
                    logger.info(`使用SOCKS5代理(带认证): ${host}:${port}`);
                } else {
                    proxyUrl = `socks5h://${host}:${port}`;
                    logger.info(`使用SOCKS5代理(无认证): ${host}:${port}`);
                }
                proxyAgent = new SocksProxyAgent(proxyUrl);
            } else {
                return {
                    status: false,
                    message: `不支持的代理类型: ${ipMold}。请使用 'http' 或 'socks5'.`
                };
            }

            // 创建axios实例，配置代理
            const axiosInstance = axios.create({
                httpsAgent: proxyAgent,
                httpAgent: proxyAgent,
                timeout: 15000, // 增加超时时间到15秒
                headers: {
                    'User-Agent': 'A2C-Client-Proxy-Test/1.0.0'
                },
                // 允许任何状态码，我们自己处理
                validateStatus: () => true
            });

            // 测试连接 - 使用更可靠的测试网站，包括国内可访问的
            const testUrls = [
                'https://ipv4.icanhazip.com',      // IP检测服务
                'https://api64.ipify.org',         // IP检测服务
                'https://www.baidu.com',           // 国内可访问的测试站点
                'https://www.qq.com',              // 另一个国内站点
                'https://httpbin.org/ip',          // 国际IP检测服务
                'https://api.ipify.org?format=json', // 备用IP检测服务
                'https://httpbin.org/get',         // HTTP测试服务
                'https://jsonplaceholder.typicode.com/posts/1' // JSON测试API
            ];

            let testResult = null;
            let testUrl = null;
            let lastError = null;

            // 延迟函数
            const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

            // 尝试多个测试URL
            for (const url of testUrls) {
                try {
                    logger.info(`尝试通过代理访问: ${url}`);
                    const startTime = Date.now();
                    const response = await axiosInstance.get(url);
                    const responseTime = Date.now() - startTime;

                    // 记录响应状态码
                    logger.info(`响应状态码: ${response.status}`);

                    // 检查响应是否成功
                    if (response.status >= 200 && response.status < 300) {
                        testResult = {
                            url: url,
                            status: response.status,
                            responseTime: responseTime,
                            data: response.data
                        };
                        testUrl = url;
                        logger.info(`测试成功: ${url}, 响应时间: ${responseTime}ms`);
                        break; // 成功就跳出循环
                    } else {
                        logger.warn(`请求返回非成功状态码: ${response.status}`);
                        // 继续尝试下一个URL
                        await delay(500); // 添加延迟，避免过快请求
                    }
                } catch (error) {
                    lastError = error;
                    logger.warn(`通过代理访问 ${url} 失败: ${error.message}`);
                    // 继续尝试下一个URL
                    await delay(500); // 添加延迟，避免过快请求
                }
            }

            if (testResult) {
                logger.info(`代理连接测试成功: ${JSON.stringify(testResult)}`);

                // 提取IP信息，不同服务返回格式不同
                let externalIp = '连接成功';
                if (typeof testResult.data === 'string' && testResult.data.trim()) {
                    // icanhazip等返回纯文本IP
                    externalIp = testResult.data.trim();
                } else if (testResult.data && (testResult.data.ip || testResult.data.origin)) {
                    // httpbin/ipify返回JSON
                    externalIp = testResult.data.ip || testResult.data.origin;
                } else if (testUrl.includes('baidu.com') || testUrl.includes('qq.com')) {
                    externalIp = '国内网站连接成功';
                }

                return {
                    status: true,
                    message: '代理服务器连接成功',
                    data: {
                        proxyType: ipMold,
                        proxyServer: `${host}:${port}`,
                        testUrl: testUrl,
                        responseTime: testResult.responseTime,
                        externalIp: externalIp
                    }
                };
            } else {
                // 根据最后的错误提供更详细的错误信息
                let errorMessage = '代理连接失败: ';
                if (lastError) {
                    if (lastError.code === 'ECONNRESET') {
                        errorMessage += '连接被重置，代理服务器可能拒绝了连接';
                    } else if (lastError.message.includes('Proxy connection ended before receiving CONNECT response')) {
                        if (ipMold === 'http') {
                            errorMessage += '代理服务器拒绝了连接请求。请检查：1) 代理地址和端口是否正确 2) 是否需要用户名密码认证 3) 代理服务器是否支持HTTPS连接';
                        } else {
                            errorMessage += '代理服务器连接中断。请检查代理地址、端口和认证信息是否正确';
                        }
                    } else if (lastError.message.includes('ECONNREFUSED')) {
                        errorMessage += '连接被拒绝，请检查代理服务器地址和端口是否正确';
                    } else if (lastError.message.includes('ETIMEDOUT')) {
                        errorMessage += '连接超时，请检查代理服务器是否正常运行或网络是否畅通';
                    } else if (lastError.message.includes('ENOTFOUND')) {
                        errorMessage += '无法解析代理服务器地址，请检查主机地址是否正确';
                    } else if (lastError.message.includes('407') || lastError.message.includes('Proxy Authentication Required')) {
                        errorMessage += '代理服务器需要身份验证，请检查用户名和密码';
                    } else {
                        errorMessage += lastError.message;
                    }
                } else {
                    errorMessage += '无法访问外部网络，请检查代理配置';
                }

                logger.error(`代理测试失败: ${errorMessage}`);
                return {
                    status: false,
                    message: errorMessage
                };
            }

        } catch (error) {
            logger.error(`代理连接测试失败: ${error.message}`);
            logger.error(`错误堆栈: ${error.stack}`);

            // 提供更详细的错误信息
            let errorMessage = '代理连接失败: ';
            if (error.code === 'ECONNREFUSED') {
                errorMessage += '连接被拒绝，请检查代理服务器地址和端口';
            } else if (error.code === 'ETIMEDOUT') {
                errorMessage += '连接超时，请检查代理服务器是否正常运行';
            } else if (error.code === 'ENOTFOUND') {
                errorMessage += '无法解析代理服务器地址';
            } else if (error.message.includes('407')) {
                errorMessage += '代理服务器需要身份验证，请检查用户名和密码';
            } else {
                errorMessage += error.message;
            }

            return {
                status: false,
                message: errorMessage,
                error: {
                    code: error.code,
                    message: error.message
                }
            };
        }
    }
}

SystemService.toString = () => '[class SystemService]';

module.exports = {
    SystemService,
    systemService: new SystemService()
};
