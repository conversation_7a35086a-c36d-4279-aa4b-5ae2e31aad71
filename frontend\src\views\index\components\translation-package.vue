<template>
  <div class="bg-white rounded-xl p6 border border-gray-100 shadow-sm">
    <h2 class="text-lg font-semibold mb-4">翻译包额度</h2>
    <div>
      <div class="flex">
        <span class="text-gray-600">普通翻译字符包剩余：</span>
        <span class="font-bold">{{ charInfo.characterPack }}</span>
      </div>
      <div class="flex mt-4">
        <span class="text-gray-600">模型翻译token包剩余：</span>
        <span class="font-bold">{{ charInfo.tokenPackage }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {charInfoApi, seatInfo} from '@/api/index/index.ts'
import {seatData} from "@/api/index/types.ts";

interface charInfoData {
  characterPack: number | string
  tokenPackage: number | string
}

const charInfo = ref<charInfoData>({
  characterPack: 0,
  tokenPackage: 0
})

const seatInfoData = ref<seatData>();

const getCharInfo = async () => {
  const res = await charInfoApi()

  if (res.code === 200) {
    charInfo.value.characterPack = (res.data.freeChars + res.data.purchasedChars) - (res.data.usedFreeChars + res.data.usedPurchasedChars)
    charInfo.value.tokenPackage = (res.data.freeTokens + res.data.purchasedTokens) - (res.data.usedFreeTokens + res.data.usedPurchasedTokens)
  }
}

const getSeatInfo = async () => {
  const res = await seatInfo();
  seatInfoData.value = res?.data;
};

onMounted(() => {
  nextTick(async () => {
    await getSeatInfo();
    console.log('seatInfo', seatInfoData.value)
    if (seatInfoData.value?.memberType !== 3) {
      charInfo.value.characterPack = '∞'
      charInfo.value.tokenPackage = '∞'
    } else {
      await getCharInfo()
    }
  })
})
</script>

<style lang="scss" scoped></style>
